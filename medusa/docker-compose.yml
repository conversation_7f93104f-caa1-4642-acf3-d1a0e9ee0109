services:
  redis:
    image: redis
    ports:
      - 6379:6379

  minio:
    image: minio/minio:latest
    ports:
      - 9090:9000 # Map host port 9090 to container port 9000 for API
      - 9001:9001 # Console port
    volumes:
      - medusa-minio-data:/data
    environment:
      MINIO_ROOT_USER: medusaminio
      MINIO_ROOT_PASSWORD: medusaminio
    command: server /data --console-address ":9001"
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9000/minio/health/live']
      interval: 30s
      timeout: 20s
      retries: 3

  createbuckets:
    image: minio/mc:latest
    depends_on:
      minio:
        condition: service_healthy
    restart: on-failure
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc alias set myminio http://minio:9000 medusaminio medusaminio;
      /usr/bin/mc mb myminio/medusa;
      /usr/bin/mc anonymous set public myminio/medusa;
      exit 0;
      "

  # Remove or comment out the Meilisearch service below
  meilisearch:
    image: getmeili/meilisearch:v1.12
    ports:
      - 7700:7700
    volumes:
      - meili-data:/meili_data
    environment:
      MEILI_MASTER_KEY: ${MEILISEARCH_MASTER_KEY}

volumes:
  medusa-postgres-data:
  medusa-minio-data:
  # Remove or comment out the Meilisearch volume below
  meili-data:
