{"name": "fashion-starter-medusa", "version": "2.0.0", "description": "A starter for Medusa projects.", "author": "Medusa (https://medusajs.com)", "license": "MIT", "keywords": ["sqlite", "postgres", "typescript", "ecommerce", "headless", "medusa"], "scripts": {"build": "medusa build && ln -s .medusa/server/public/ public", "seed": "medusa exec ./src/scripts/seed.ts", "start": "medusa start", "dev": "medusa develop", "emails:dev": "email dev --dir=src/modules/resend/emails", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit"}, "dependencies": {"@medusajs/admin-sdk": "2.6.1", "@medusajs/cli": "2.6.1", "@medusajs/framework": "2.6.1", "@medusajs/medusa": "2.6.1", "@mikro-orm/core": "6.4.3", "@mikro-orm/knex": "6.4.3", "@mikro-orm/migrations": "6.4.3", "@mikro-orm/postgresql": "6.4.3", "@react-email/components": "^0.0.33", "@tanstack/react-query": "5.64.2", "awilix": "^8.0.1", "lodash": "^4.17.21", "meilisearch": "0.47.0", "pg": "^8.13.3", "react-dropzone": "^14.3.8", "resend": "^4.1.2", "zod": "3.22.4"}, "devDependencies": {"@medusajs/test-utils": "2.6.1", "@mikro-orm/cli": "6.4.3", "@swc/core": "1.5.7", "@swc/jest": "^0.2.37", "@tanstack/eslint-plugin-query": "5.64.2", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^22.13.10", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^9.17.0", "jest": "^29.7.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-email": "3.0.7", "ts-node": "^10.9.2", "typescript": "^5.8.2", "vite": "^5.4.14"}, "engines": {"node": ">=20"}, "packageManager": "yarn@4.7.0"}