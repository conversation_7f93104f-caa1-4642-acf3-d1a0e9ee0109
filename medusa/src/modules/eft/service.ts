import { AbstractPaymentProvider } from "@medusajs/framework/utils"
import { <PERSON><PERSON> } from "@medusajs/framework/types"
import {
  InitiatePaymentInput,
  InitiatePaymentOutput,
  AuthorizePaymentInput,
  AuthorizePaymentOutput,
  CapturePaymentInput,
  CapturePaymentOutput,
  CancelPaymentInput,
  CancelPaymentOutput,
  DeletePaymentInput,
  DeletePaymentOutput,
  RetrievePaymentInput,
  RetrievePaymentOutput,
  UpdatePaymentInput,
  UpdatePaymentOutput,
} from "@medusajs/framework/types"
import { EftOptions, EftPaymentData } from "./types"

type InjectedDependencies = {
  logger: Logger
}

class EftPaymentProviderService extends AbstractPaymentProvider<EftOptions> {
  static identifier = "eft"

  protected logger_: Logger
  protected options_: EftOptions

  constructor(
    container: InjectedDependencies,
    options: EftOptions
  ) {
    super(container, options)

    this.logger_ = container.logger
    this.options_ = options
  }

  /**
   * Generate a random 8-character alphanumeric reference
   */
  private generatePaymentReference(): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  async initiatePayment(
    input: InitiatePaymentInput
  ): Promise<InitiatePaymentOutput> {
    const { amount, currency_code, context } = input

    const reference = this.generatePaymentReference()

    const paymentData: EftPaymentData = {
      reference,
      amount,
      currency_code,
      customer_email: context?.customer?.email || '',
      created_at: new Date(),
    }

    this.logger_.info(`EFT payment initiated with reference: ${reference}`)

    return {
      id: reference,
      data: paymentData,
    }
  }

  async authorizePayment(
    input: AuthorizePaymentInput
  ): Promise<AuthorizePaymentOutput> {
    const paymentData = input.data as EftPaymentData

    // Send email with payment instructions
    try {
      await this.sendPaymentInstructions(paymentData, input.context)
      this.logger_.info(`EFT payment instructions sent for reference: ${paymentData.reference}`)
    } catch (error) {
      this.logger_.error(`Failed to send EFT payment instructions: ${error}`)
    }

    return {
      data: {
        ...paymentData,
        status: "pending"
      },
      status: "pending"
    }
  }

  async capturePayment(
    input: CapturePaymentInput
  ): Promise<CapturePaymentOutput> {
    const paymentData = input.data as EftPaymentData

    this.logger_.info(`EFT payment captured for reference: ${paymentData.reference}`)

    return {
      data: {
        ...paymentData,
        status: "captured"
      }
    }
  }

  async cancelPayment(
    input: CancelPaymentInput
  ): Promise<CancelPaymentOutput> {
    const paymentData = input.data as EftPaymentData

    this.logger_.info(`EFT payment cancelled for reference: ${paymentData.reference}`)

    return {
      data: {
        ...paymentData,
        status: "cancelled"
      }
    }
  }

  async deletePayment(
    input: DeletePaymentInput
  ): Promise<DeletePaymentOutput> {
    const paymentData = input.data as EftPaymentData

    this.logger_.info(`EFT payment deleted for reference: ${paymentData.reference}`)

    return {
      data: paymentData
    }
  }

  async retrievePayment(
    input: RetrievePaymentInput
  ): Promise<RetrievePaymentOutput> {
    const paymentData = input.data as EftPaymentData

    return {
      id: paymentData.reference,
      data: paymentData
    }
  }

  async updatePayment(
    input: UpdatePaymentInput
  ): Promise<UpdatePaymentOutput> {
    const { amount, currency_code } = input
    const paymentData = input.data as EftPaymentData

    const updatedData: EftPaymentData = {
      ...paymentData,
      amount: amount || paymentData.amount,
      currency_code: currency_code || paymentData.currency_code,
    }

    this.logger_.info(`EFT payment updated for reference: ${paymentData.reference}`)

    return {
      data: updatedData
    }
  }

  /**
   * Send payment instructions email to customer
   */
  private async sendPaymentInstructions(
    paymentData: EftPaymentData,
    context: any
  ): Promise<void> {
    // For now, we'll log the payment instructions
    // In a real implementation, you would integrate with the notification service
    this.logger_.info("EFT Payment Instructions:", {
      reference: paymentData.reference,
      amount: paymentData.amount,
      currency_code: paymentData.currency_code,
      customer_email: paymentData.customer_email,
      order: context?.order || {},
      customer: context?.customer || {},
    })

    // TODO: Integrate with notification service to send actual email
    // This would require proper dependency injection of the notification service
    this.logger_.info(`EFT payment instructions should be sent to: ${paymentData.customer_email}`)
  }
}

export default EftPaymentProviderService
