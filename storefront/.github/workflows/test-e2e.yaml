name: Medusa NextJS Template Tests

on:
  push:
    branches:
      - main
  pull_request:
  workflow_dispatch:

env:
  PGHOST: localhost
  PGPORT: 5432
  PGUSER: postgres
  PGPASSWORD: password
  PGDATABASE: postgres

  TEST_POSTGRES_USER: test_medusa_user
  TEST_POSTGRES_PASSWORD: password
  TEST_POSTGRES_DATABASE: test_medusa_db
  TEST_POSTGRES_DATABASE_TEMPLATE: test_medusa_db_template
  TEST_POSTGRES_HOST: localhost
  TEST_POSTGREST_PORT: 5432
  PRODUCTION_POSTGRES_DATABASE: medusa_db
  CLIENT_SERVER: http://localhost:9000

  JWT_SECRET: something
  COOKIE_SECRET: something

  DATABASE_TYPE: "postgres"
  REDIS_URL: redis://localhost:6379
  DATABASE_URL: postgres://test_medusa_user:password@localhost/test_medusa_db
  MEILISEARCH_HOST: http://localhost:7700
  MEILISEARCH_API_KEY: meili_api_key

  NEXT_PUBLIC_BASE_URL: http://localhost:8000
  NEXT_PUBLIC_DEFAULT_REGION: us
  NEXT_PUBLIC_MEDUSA_BACKEND_URL: http://localhost:9000
  NEXT_PUBLIC_INDEX_NAME: products
  NEXT_PUBLIC_SEARCH_ENDPOINT: http://127.0.0.1:7700
  NEXT_PUBLIC_SEARCH_API_KEY: meili_api_key
  REVALIDATE_SECRET: supersecret

jobs:
  e2e-test-runner:
    timeout-minutes: 20
    runs-on:
      - ubuntu-latest
    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
          POSTGRES_DB: test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      meilisearch:
        image: getmeili/meilisearch:v1.7
        env:
          MEILI_MASTER_KEY: meili_api_key
          MEILI_ENV: development
        ports:
          - 7700:7700
        options: >-
          --health-cmd "curl --fail http://localhost:7700/health"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:latest
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v2

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Initialize PostgreSQL
        run: |
          echo "Initializing Databases"
          psql -h localhost -U postgres -d test -c "CREATE USER ${{ env.TEST_POSTGRES_USER }} WITH PASSWORD '${{ env.TEST_POSTGRES_PASSWORD }}';"
          psql -h localhost -U postgres -d test -c "CREATE DATABASE ${{ env.TEST_POSTGRES_DATABASE }} OWNER ${{ env.TEST_POSTGRES_USER }};"

      - name: Install Medusa CLI
        run: npm install @medusajs/medusa-cli@preview -g
      - name: Setup medusa backend server
        working-directory: ../
        # https://docs.medusajs.com/cli/reference#options
        run: |
          medusa new backend \
          -y \
          --v2 \
          --branch feat/v2 \
          --skip-db \
          --skip-migrations \
          --skip-env \
          --db-user ${{ env.TEST_POSTGRES_USER }} \
          --db-pass ${{ env.TEST_POSTGRES_PASSWORD }} \
          --db-database ${{ env.TEST_POSTGRES_DATABASE }} \
          --db-host ${{ env.TEST_POSTGRES_HOST }} \
          --db-port ${{ env.TEST_POSTGREST_PORT }}

      - name: Setup search in the backend
        working-directory: ../backend
        run: yarn add medusa-plugin-meilisearch

      - name: Move custom medusa config to the backend
        run: cp .github/scripts/medusa-config.js ../backend/medusa-config.js

      - name: Seed data from default seed file
        working-directory: ../backend
        run: medusa seed --seed-file=data/seed.json

      - name: Run backend server
        working-directory: ../backend
        run: medusa develop

      - name: Install packages
        run: yarn install -y

      - name: Install playwright
        run: yarn playwright install --with-deps

      - name: Setup frontend
        run: yarn build

      - name: Run Tests
        run: yarn test-e2e

      - uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: test-results
          retention-days: 30
