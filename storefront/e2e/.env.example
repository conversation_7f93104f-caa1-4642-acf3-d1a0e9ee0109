# Need a superuser to reset database
PGHOST=localhost
PGPORT=5432
PGUSER=postgres
PGPASSWORD=password
PGDATABASE=postgres

# Test database config
TEST_POSTGRES_USER=test_medusa_user
TEST_POSTGRES_DATABASE=test_medusa_db
TEST_POSTGRES_DATABASE_TEMPLATE=test_medusa_db_template
TEST_POSTGRES_HOST=localhost
TEST_POSTGREST_PORT=5432
PRODUCTION_POSTGRES_DATABASE=medusa_db

# Backend server API
CLIENT_SERVER=http://localhost:9000
MEDUSA_ADMIN_EMAIL=<EMAIL>
MEDUSA_ADMIN_PASSWORD=supersecret