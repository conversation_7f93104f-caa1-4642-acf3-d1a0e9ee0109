import { Metadata } from "next"
import Image from "next/image"
import { getRegion } from "@lib/data/regions"
import { getProductTypesList } from "@lib/data/product-types"
import { Layout, LayoutColumn } from "@/components/Layout"
import { LocalizedLink } from "@/components/LocalizedLink"
import { CollectionsSection } from "@/components/CollectionsSection"

export const metadata: Metadata = {
  title: "Hairven Beauty",
  description:
    "The Hairven Beauty Online Store.One stop place for luxury hair, for boss ladies.",
}

const ProductTypesSection: React.FC = async () => {
  const productTypes = await getProductTypesList(0, 20, [
    "id",
    "value",
    "metadata",
  ])

  if (!productTypes) {
    return null
  }

  return (
    <Layout className="mb-26 md:mb-36 max-md:gap-x-2">
      <LayoutColumn>
        <h3 className="text-sm md:text-xl mb-4 md:mb-12">Our products</h3>
      </LayoutColumn>
      {productTypes.productTypes.map((productType, index) => (
        <LayoutColumn
          key={productType.id}
          start={index % 2 === 0 ? 1 : 7}
          end={index % 2 === 0 ? 7 : 13}
        >
          <LocalizedLink href={`/store?type=${productType.value}`}>
            {typeof productType.metadata?.image === "object" &&
              productType.metadata.image &&
              "url" in productType.metadata.image &&
              typeof productType.metadata.image.url === "string" && (
                <Image
                  src={productType.metadata.image.url}
                  width={800}
                  height={600}
                  alt={productType.value}
                  className="mb-2 md:mb-2"
                />
              )}
            <p className="text-xs md:text-md mb-2 md:mb-6">
              {productType.value}
            </p>
          </LocalizedLink>
        </LayoutColumn>
      ))}
    </Layout>
  )
}

export default async function Home({
  params,
}: {
  params: Promise<{ countryCode: string }>
}) {
  const { countryCode } = await params
  const region = await getRegion(countryCode)

  if (!region) {
    return null
  }

  return (
    <>
      <div className="max-md:pt-18">
        <Image
          src="/images/content/hb2.jpg"
          width={2880}
          height={1500}
          alt="Hairven Beauty One"
          className="md:h-screen md:object-cover"
        />
      </div>
      <div className="pt-8 pb-26 md:pt-26 md:pb-36">
        <Layout className="mb-26 md:mb-36">
          <LayoutColumn start={1} end={{ base: 13, md: 8 }}>
            <h3 className="text-sm max-md:mb-6 md:text-xl">
              Elevate Your Look & Style with unparallelled quality from Hairven
              Beauty!
            </h3>
          </LayoutColumn>
          <LayoutColumn start={{ base: 1, md: 9 }} end={13}>
            <div className="flex items-center h-full">
              <div className="md:text-md">
                <p>Discover Your best hair products today</p>
                <LocalizedLink href="/store" variant="underline">
                  Explore Now
                </LocalizedLink>
              </div>
            </div>
          </LayoutColumn>
        </Layout>
        <ProductTypesSection />
        <CollectionsSection className="mb-22 md:mb-36" />
        <Layout>
          <LayoutColumn className="col-span-full">
            <h3 className="text-sm md:text-xl mb-8 md:mb-16">
              About Hairven Beauty
            </h3>
            <Image
              src="/images/content/shop-3.png"
              width={2496}
              height={1100}
              alt="HairvenBeauty Shop"
              className="mb-8 md:mb-16 max-md:aspect-[3/2] max-md:object-cover
              grayscale [filter:sepia(20%)_hue-rotate(60deg)_saturate(30%)]
              "
            />
          </LayoutColumn>
          <LayoutColumn start={1} end={{ base: 13, md: 7 }}>
            <h2 className="text-sm md:text-xl">
              At Hairven Beauty, Excellence is in everything we do. When its
              beauty, We go all out for you.
            </h2>
          </LayoutColumn>
          <LayoutColumn
            start={{ base: 1, md: 8 }}
            end={13}
            className="mt-6 md:mt-19"
          >
            <div className="md:text-md">
              <p className="mb-5 md:mb-9">
                We are dedicated to delivering high-quality, thoughtfully
                crafted and designed hair products that merge comfort, elegance
                and style effortlessly.
              </p>
              <p className="mb-5 md:mb-3">
                We aim to be a core part of that inexplicable beauty
                transformation you long for with our products that are expertly
                sourced ad designed to last.
              </p>
              <LocalizedLink href="/about" variant="underline">
                Read more about Hairven Beauty
              </LocalizedLink>
            </div>
          </LayoutColumn>
        </Layout>
      </div>
    </>
  )
}
