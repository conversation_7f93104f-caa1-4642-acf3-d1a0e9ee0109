import React from "react"

import { IconProps } from "types/icon"

const HairvenLogoAlt: React.FC<IconProps> = ({
  size = "20",
  color = "currentColor",
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.0"
      viewBox="0 0 382 398"
      width={size}
      height={size}
      fill={color}
    >
      <g fill="black" strokeWidth="0">
        <path d="M157.1 183c-.2 4.6-.5 5.9-1.1 4.5-.5-1.1-.9-1.4-.9-.7-.2 2.3 3.2 7.9 3.7 6.2.3-.7.1-1.5-.4-1.7s-.7-2.5-.6-5.2c.2-2.6.2-5.9-.1-7.2-.2-1.3-.5.5-.6 4.1m-37.7 23.1c-.3.6-.1 1.6.5 2.2.9.9 1.1.8.6-.5-.6-1.6.4-1.8 10.2-2.1l10.8-.3-10.7-.2c-6.8-.1-10.9.2-11.4.9m40.3-.4c-.3.5 1.8.7 4.6.5 9.2-.5 10.4-.9 3-1.1-3.9 0-7.4.2-7.6.6M126.4 237c0 15.7.2 22.1.3 14.2.2-7.8.2-20.6 0-28.5-.1-7.8-.3-1.4-.3 14.3m9.7-21.8c0 3.7.2 6.9.7 7.1.4.3.6-1.8.4-4.6-.5-8.6-.9-9.6-1.1-2.5m69.3 5.8c0 5.2.1 7.4.3 4.7.2-2.6.2-6.8 0-9.5-.2-2.6-.3-.4-.3 4.8m-10.2 21.2-.2 29.7-3.2.3c-2.1.2-3.3.8-3.3 1.8 0 1.2 1.5 1.5 7.5 1.6l7.5.1-7.2-.4c-4-.2-7.3-.8-7.3-1.3 0-.6 1.4-1 3-1 1.7 0 3.3-.6 3.5-1.3.3-.7.4-14.3.2-30.2l-.2-29z" />
        <path d="m136.2 251.7.3 20.8 3.3.3c1.7.2 3.2.7 3.2 1.3 0 .5-5 .9-11.5.9s-11.5-.4-11.5-.9 1.1-1.2 2.5-1.4c2.3-.4 2.3-.4-.2-.3-1.8.1-2.8.7-2.8 1.6 0 1.2 2 1.5 12 1.5s12-.3 12-1.5c0-1-1.2-1.6-3.2-1.8l-3.3-.3V252c0-18.1.2-19.9 1.8-20.3 1.3-.4 1.1-.5-.6-.6l-2.2-.1zm69 .5.3 20.3 2.5.1c2.2.1 2.2 0 .3-.3-2.3-.4-2.3-.5-2.3-19.8 0-14.5.3-19.5 1.3-19.8.7-.3.5-.6-.6-.6-1.6-.1-1.7 1.3-1.5 20.1m-39.9 23.5c3.1.2 8.3.2 11.5 0 3.1-.2.5-.3-5.8-.3s-8.9.1-5.7.3" />
      </g>
      <g fill="black" strokeWidth="0">
        <path d="M186.6 164.2c-1.1 4.3-.6 12.9 1 16.6l1.6 3.7 1.3-4.8c1.5-5.6 3.6-8.1 8.5-10.8 2.1-1.1 2.8-1.8 1.8-1.9-2.5 0-10.8 8.2-10.8 10.8 0 1.1-.4 2.3-.9 2.7-1.5.8-2.4-5.9-1.8-12.6.5-6.2.3-7.4-.7-3.7m-12.9 12c-.3 2.4-.2 5.9.2 7.8.7 2.8.8 2 .9-4.3.1-4.2 0-7.7-.2-7.7s-.6 1.9-.9 4.2" />
        <path d="M166.6 177.1c-1.7 10.5 4.3 22.2 13.9 27.1 4.8 2.5 14.5 3.4 20.1 1.9 4.7-1.3 12.9-6.5 13.8-8.7.5-1.2-.2-1.1-2.9.6-1.9 1.2-4.8 2.6-6.3 3l-2.7.8 2.5.2c2.5.1 2.5.1-.5 1.8-4.7 2.6-18.8 2.3-24.2-.6-8-4.2-12.6-12.6-12.5-22.9 0-4-.1-7.3-.2-7.3-.2 0-.6 1.8-1 4.1" />
        <path d="M175 188.8c0 2 2.6 6.5 5.2 8.9 3.8 3.6 9.2 5.3 16 5.1l6.3-.1-7.6-.6c-9.5-.8-15.1-4.1-18-10.4-1-2.3-1.9-3.6-1.9-2.9m-55 18.1c0 .6 1.5 1.1 3.3 1.3l3.2.3v64l-3.2.3c-1.8.2-3.3.7-3.3 1.3 0 .5 5 .9 11.5.9s11.5-.4 11.5-.9c0-.6-1.5-1.1-3.2-1.3l-3.3-.3-.3-20.7-.2-20.6 7.3.5c8.3.6 13.4-.6 16.3-3.9 1.8-2 1.8-2.1.1-1.5-1 .4-2.6.7-3.5.7-1.4 0-1.5.2-.2 1 2.7 1.7-4 3.2-12.5 2.8l-8-.3V274h-8v-67h7.5v7.4c0 5.5.4 7.9 1.6 9.1 2.3 2.2 8.5 4.7 10.9 4.2 1.6-.4 1.5-.5-.5-.6-1.4-.1-4.3-1.2-6.5-2.4l-4-2.3v-6.9c0-6.9 0-7 2.8-7.3 1.5-.2 2.5-.8 2.2-1.3-.8-1.2-21.5-1.2-21.5 0" />
        <path d="M242 265.2c-.5 5.3-1.2 7.7-3.6 11-6.6 9.4-20 12.5-31.3 7.2-4.9-2.2-5.3-2.3-3.7-.5 5.4 6 25.2 7.8 35.3 3.2 5.1-2.3 12.3-8.9 12.3-11.3 0-.6-1.4.8-3.1 3.1-3.8 5.3-11.2 8.9-20 9.7l-6.4.5 5.9-2.5c10.7-4.6 16-12.2 15.4-22.2-.3-4.7-.3-4.7-.8 1.8" />
      </g>
      <g fill="black" strokeWidth="0">
        <path d="M196 117.1c0 .3.7 2.7 1.6 5.3 1.2 3.4 1.5 7 1.2 12.5-.4 7-.9 8.6-5.7 17.5-5.6 10.3-7.2 17.2-5.7 25 .7 3.8 2.6 4.1 2.6.4 0-1.2 1.9-4 4.3-6.3 3.6-3.4 5.6-4.4 11.3-5.6l6.9-1.5v-11.8c0-10.5-.2-12-1.8-13-2.6-1.5-7-8.3-8.6-13.1-1.1-3.4-6.1-11.1-6.1-9.4" />
        <path d="M190.1 133.2c0 .9-3.3 6.5-7.3 12.5-3.9 5.9-8.9 14.5-11 19.1-3.4 7.2-4 9.3-4 15.5-.1 10.3 4.5 18.7 12.5 22.9 5.4 2.9 19.5 3.2 24.2.6 3-1.7 2.9-1.7-5.1-1.1-15.2 1.1-22.4-3.9-25.3-17.5-1.8-8.5.4-16.4 9.4-34 5.2-10.1 7.5-15.7 7.2-17.3-.2-1.3-.5-1.7-.6-.7m-13.7 7.5c-4.7 6.4-5 7.1-1.6 3.4 3.3-3.6 5.7-7.1 4.8-7.1-.3 0-1.7 1.7-3.2 3.7m-48.9 99.8V274h8v-43.5h9.7c10.6 0 15.8-1.9 6-2.2-6-.1-11.9-2.1-14.6-4.8-1.2-1.2-1.6-3.6-1.6-9.1V207h-7.5zm39.8-.1.2 33.6h7.5l-.2-33.3-.3-33.2-3.7-.3-3.8-.3zm51.8-31c9.1 3.2 12.9 8.3 12.9 17.2 0 4.9.2 5.5 1.5 4.4 2.8-2.3 3.9-8.2 2.2-12.2-2.4-5.7-10.9-10.9-17.6-10.8-2.3.1-2.2.2 1 1.4m-20.3 3.3c-1.7.4-1.8 2.7-1.8 29.3 0 15.9-.3 29.5-.6 30.4-.5 1.3.2 1.6 3.7 1.6h4.4l-.2-29.7c-.2-16.4-.5-30.4-.8-31.1-.4-1.2-1.6-1.3-4.7-.5m35.5 32.1c5.9 6.4 7.8 10.3 8.4 17.4q1.5 16.2-15.3 23.4l-5.9 2.5 6.4-.5c8.9-.8 16.2-4.4 20.1-9.8 3-4 3.2-4.8 2.8-11.3-.3-6-.8-7.9-3.8-12.4-3.1-5-13.7-14.1-16.2-14.1-.6 0 1 2.2 3.5 4.8" />
      </g>
    </svg>
  )
}

export default HairvenLogoAlt
