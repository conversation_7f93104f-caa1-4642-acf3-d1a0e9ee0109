import * as React from "react"

export const Loader: React.FC<React.ComponentPropsWithoutRef<"svg">> = (
  props
) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 25 25"
    fill="none"
  >
    <path
      d="M25 12.5C25 19.4036 19.4036 25 12.5 25C5.59644 25 0 19.4036 0 12.5C0 5.59644 5.59644 0 12.5 0C19.4036 0 25 5.59644 25 12.5ZM3.125 12.5C3.125 17.6777 7.32233 21.875 12.5 21.875C17.6777 21.875 21.875 17.6777 21.875 12.5C21.875 7.32233 17.6777 3.125 12.5 3.125C7.32233 3.125 3.125 7.32233 3.125 12.5Z"
      opacity="0.3"
      fill="currentColor"
    />
    <path
      d="M1.5625 12.5C0.699555 12.5 -0.0100737 11.7977 0.0975115 10.9415C0.400499 8.53018 1.40194 6.24704 2.99493 4.3819C4.92787 2.11871 7.60492 0.61949 10.5446 0.153896C13.4842 -0.311698 16.4935 0.286891 19.0312 1.842C21.1226 3.1236 22.7806 4.98552 23.8139 7.18519C24.1808 7.96625 23.7229 8.85346 22.9022 9.12013V9.12013C22.0815 9.38679 21.2111 8.92899 20.812 8.16389C20.0309 6.66663 18.8548 5.39896 17.3984 4.5065C15.4951 3.34017 13.2382 2.89123 11.0334 3.24042C8.82869 3.58962 6.8209 4.71403 5.37119 6.41142C4.2619 7.71024 3.53507 9.27931 3.2549 10.9447C3.11173 11.7957 2.42544 12.5 1.5625 12.5V12.5Z"
      fill="currentColor"
    />
  </svg>
)
