import * as React from "react"

export const MapPin: React.FC<React.ComponentPropsWithoutRef<"svg">> = (
  props
) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M11.6841 21.4314C11.7355 21.4733 11.7931 21.5035 11.8535 21.5219C11.7535 21.5526 11.6643 21.6143 11.6 21.7L11.5974 21.7035L11.5947 21.707L11.5921 21.7105L11.5895 21.714L11.5869 21.7174L11.5844 21.7208L11.5818 21.7243L11.5792 21.7277L11.5767 21.7311L11.5741 21.7345L11.5716 21.7378L11.5691 21.7412L11.5666 21.7446L11.5641 21.7479L11.5616 21.7512L11.5591 21.7545L11.5566 21.7578L11.5542 21.7611L11.5517 21.7643L11.5493 21.7676L11.5469 21.7708L11.5444 21.7741L11.542 21.7773L11.5396 21.7805L11.5372 21.7837L11.5349 21.7868L11.5325 21.79L11.5301 21.7932L11.5278 21.7963L11.5254 21.7994L11.5231 21.8025L11.5208 21.8056L11.5185 21.8087L11.5162 21.8118L11.5139 21.8148L11.5116 21.8179L11.5093 21.8209L11.5071 21.8239L11.5048 21.8269L11.5026 21.8299L11.5003 21.8329L11.4981 21.8359L11.4959 21.8388L11.4937 21.8418L11.4915 21.8447L11.4893 21.8476L11.4871 21.8505L11.4849 21.8534L11.4828 21.8563L11.4806 21.8592L11.4785 21.862L11.4763 21.8649L11.4742 21.8677L11.4721 21.8705L11.47 21.8733L11.4679 21.8761L11.4658 21.8789L11.4637 21.8817L11.4617 21.8844L11.4596 21.8872L11.4576 21.8899L11.4569 21.8909C11.2386 21.7147 10.9396 21.4668 10.5854 21.1569C9.82987 20.4958 8.8219 19.5509 7.81315 18.4161C5.80744 16.1597 3.75 13.0988 3.75 10C3.75 7.81196 4.61919 5.71354 6.16637 4.16637C7.71354 2.61919 9.81196 1.75 12 1.75C14.188 1.75 16.2865 2.61919 17.8336 4.16637C19.3808 5.71354 20.25 7.81196 20.25 10C20.25 13.0988 18.1926 16.1597 16.1869 18.4161C15.1781 19.5509 14.1701 20.4958 13.4146 21.1569C13.0605 21.4668 12.7614 21.7146 12.5432 21.8908C12.5014 21.8352 12.4539 21.7719 12.4 21.7C12.3357 21.6143 12.2465 21.5526 12.1465 21.5219C12.2069 21.5035 12.2645 21.4733 12.3159 21.4314C12.5196 21.2653 12.7818 21.0463 13.0854 20.7806C13.8299 20.1292 14.8219 19.1991 15.8131 18.0839C17.8073 15.8405 19.75 12.9013 19.75 10C19.75 7.94457 18.9335 5.97333 17.4801 4.51992C16.0267 3.06652 14.0554 2.25 12 2.25C9.94457 2.25 7.97333 3.06652 6.51992 4.51992C5.06652 5.97333 4.25 7.94457 4.25 10C4.25 12.9013 6.19269 15.8405 8.18685 18.0839C9.17811 19.1991 10.1701 20.1292 10.9146 20.7806C11.2182 21.0463 11.4804 21.2653 11.6841 21.4314ZM12 7.25C10.4812 7.25 9.25 8.48122 9.25 10C9.25 11.5188 10.4812 12.75 12 12.75C13.5188 12.75 14.75 11.5188 14.75 10C14.75 8.48122 13.5188 7.25 12 7.25ZM8.75 10C8.75 8.20507 10.2051 6.75 12 6.75C13.7949 6.75 15.25 8.20507 15.25 10C15.25 11.7949 13.7949 13.25 12 13.25C10.2051 13.25 8.75 11.7949 8.75 10Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
