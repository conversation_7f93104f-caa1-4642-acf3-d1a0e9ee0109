import * as React from "react"

export const Receipt: React.FC<React.ComponentPropsWithoutRef<"svg">> = (
  props
) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M5.77639 2.60869C5.91716 2.67907 6.08284 2.67907 6.22361 2.60869L7.8882 1.77639C7.95858 1.7412 8.04142 1.7412 8.1118 1.77639L9.77639 2.60869C9.91716 2.67907 10.0828 2.67907 10.2236 2.60869L11.8882 1.77639C11.9586 1.7412 12.0414 1.7412 12.1118 1.77639L13.7764 2.60869C13.9172 2.67907 14.0828 2.67907 14.2236 2.60869L15.8882 1.77639C15.9586 1.7412 16.0414 1.7412 16.1118 1.77639L17.7764 2.60869C17.9172 2.67907 18.0828 2.67907 18.2236 2.60869L19.8882 1.77639L19.6662 1.33249L19.8882 1.77639C19.9657 1.73765 20.0577 1.74179 20.1314 1.78734C20.2051 1.83289 20.25 1.91336 20.25 2V22C20.25 22.0866 20.2051 22.1671 20.1314 22.2127C20.0577 22.2582 19.9657 22.2624 19.8882 22.2236L18.2236 21.3913C18.0828 21.3209 17.9172 21.3209 17.7764 21.3913L16.1118 22.2236C16.0414 22.2588 15.9586 22.2588 15.8882 22.2236L14.2236 21.3913C14.0828 21.3209 13.9172 21.3209 13.7764 21.3913L12.1118 22.2236C12.0414 22.2588 11.9586 22.2588 11.8882 22.2236L10.2236 21.3913C10.0828 21.3209 9.91716 21.3209 9.77639 21.3913L8.1118 22.2236L8.33541 22.6708L8.1118 22.2236C8.04142 22.2588 7.95858 22.2588 7.8882 22.2236L7.66459 22.6708L7.8882 22.2236L6.22361 21.3913C6.08284 21.3209 5.91716 21.3209 5.77639 21.3913L4.1118 22.2236C4.03431 22.2624 3.94227 22.2582 3.86857 22.2127C3.79486 22.1671 3.75 22.0866 3.75 22V2C3.75 1.91336 3.79486 1.83289 3.86857 1.78734C3.94227 1.74179 4.03431 1.73765 4.1118 1.77639L5.77639 2.60869ZM4.97361 2.76631C4.81861 2.68882 4.63454 2.6971 4.48713 2.7882C4.33973 2.8793 4.25 3.04024 4.25 3.21353V20.7865C4.25 20.9598 4.33973 21.1207 4.48713 21.2118C4.63454 21.3029 4.81861 21.3112 4.97361 21.2337L5.8882 20.7764L5.66459 20.3292L5.8882 20.7764C5.95858 20.7412 6.04142 20.7412 6.1118 20.7764L6.33541 20.3292L6.1118 20.7764L7.77639 21.6087C7.91716 21.6791 8.08284 21.6791 8.22361 21.6087L9.8882 20.7764L9.66459 20.3292L9.8882 20.7764C9.95858 20.7412 10.0414 20.7412 10.1118 20.7764L10.3354 20.3292L10.1118 20.7764L11.7764 21.6087C11.9172 21.6791 12.0828 21.6791 12.2236 21.6087L13.8882 20.7764C13.9586 20.7412 14.0414 20.7412 14.1118 20.7764L15.7764 21.6087C15.9172 21.6791 16.0828 21.6791 16.2236 21.6087L17.8882 20.7764C17.9586 20.7412 18.0414 20.7412 18.1118 20.7764L19.0264 21.2337C19.1814 21.3112 19.3655 21.3029 19.5129 21.2118C19.6603 21.1207 19.75 20.9598 19.75 20.7865V3.21353C19.75 3.04024 19.6603 2.8793 19.5129 2.7882C19.3655 2.6971 19.1814 2.68882 19.0264 2.76631L18.1118 3.22361C18.0414 3.2588 17.9586 3.2588 17.8882 3.22361L16.2236 2.39131C16.0828 2.32093 15.9172 2.32093 15.7764 2.39131L14.1118 3.22361C14.0414 3.2588 13.9586 3.2588 13.8882 3.22361L12.2236 2.39131C12.0828 2.32093 11.9172 2.32093 11.7764 2.39131L10.1118 3.22361C10.0414 3.2588 9.95858 3.2588 9.8882 3.22361L8.22361 2.39131C8.08284 2.32093 7.91716 2.32093 7.77639 2.39131L6.1118 3.22361C6.04142 3.2588 5.95858 3.2588 5.8882 3.22361L4.97361 2.76631ZM16.25 8.25H13.25V7.75H16.25V8.25ZM12.25 7.75V8.25H11.75V7.75H12.25ZM11.75 9.25H12.25V10.75H11.75V9.25ZM11.75 11.75H12.25V12.25H11.75V11.75ZM11.75 13.25H12.25V14.75H11.75V13.25ZM13.25 15.75H14C14.4641 15.75 14.9092 15.5656 15.2374 15.2374C15.5656 14.9092 15.75 14.4641 15.75 14C15.75 13.5359 15.5656 13.0908 15.2374 12.7626C14.9092 12.4344 14.4641 12.25 14 12.25H13.25V11.75H14C14.5967 11.75 15.169 11.9871 15.591 12.409C16.0129 12.831 16.25 13.4033 16.25 14C16.25 14.5967 16.0129 15.169 15.591 15.591C15.169 16.0129 14.5967 16.25 14 16.25H13.25V15.75ZM11.75 15.75H12.25V16.25H11.75V15.75ZM10.75 15.75V16.25H7.75V15.75H10.75ZM10.75 12.25H10C9.40326 12.25 8.83097 12.0129 8.40901 11.591C7.98705 11.169 7.75 10.5967 7.75 10C7.75 9.40326 7.98705 8.83097 8.40901 8.40901C8.83097 7.98705 9.40326 7.75 10 7.75H10.75V8.25H10C9.53587 8.25 9.09075 8.43437 8.76256 8.76256C8.43438 9.09075 8.25 9.53587 8.25 10C8.25 10.4641 8.43438 10.9092 8.76256 11.2374C9.09075 11.5656 9.53587 11.75 10 11.75H10.75V12.25Z"
      stroke="currentColor"
      strokeLinecap="square"
      strokeLinejoin="round"
    />
  </svg>
)
