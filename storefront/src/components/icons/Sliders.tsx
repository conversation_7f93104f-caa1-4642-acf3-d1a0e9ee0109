import * as React from "react"

export const Sliders: React.FC<React.ComponentPropsWithoutRef<"svg">> = (
  props
) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M6.889 2a.55.55 0 0 1 .555.545v3.273a.55.55 0 0 1-.555.546.55.55 0 0 1-.556-.546V4.182H3.556A.55.55 0 0 1 3 3.636a.55.55 0 0 1 .556-.545h2.777v-.546A.55.55 0 0 1 6.89 2Zm1.667 1.636a.55.55 0 0 1 .555-.545h3.333a.55.55 0 0 1 .556.545.55.55 0 0 1-.556.546H9.111a.55.55 0 0 1-.555-.546Zm1.11 2.728a.55.55 0 0 1 .556.545v.546h2.222A.55.55 0 0 1 13 8a.55.55 0 0 1-.556.545h-2.222v.546a.55.55 0 0 1-.555.545.55.55 0 0 1-.556-.545V6.909a.55.55 0 0 1 .556-.545ZM3 8a.55.55 0 0 1 .556-.545H8A.55.55 0 0 1 8.556 8 .55.55 0 0 1 8 8.545H3.556A.55.55 0 0 1 3 8Zm3.333 1.636a.55.55 0 0 1 .556.546v3.272a.55.55 0 0 1-.556.546.55.55 0 0 1-.555-.546v-.545H3.556A.55.55 0 0 1 3 12.364a.55.55 0 0 1 .556-.546h2.222v-1.636a.55.55 0 0 1 .555-.546Zm1.111 2.728A.55.55 0 0 1 8 11.818h4.444a.55.55 0 0 1 .556.546.55.55 0 0 1-.556.545H8a.55.55 0 0 1-.556-.545Z"
      clipRule="evenodd"
    />
  </svg>
)
