import * as React from "react"

export const Truck: React.FC<React.ComponentPropsWithoutRef<"svg">> = (
  props
) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M14.327 17.707a3.251 3.251 0 0 0 6.414-.457H22a.75.75 0 0 0 .75-.75v-3.34l-8.423 4.547Zm0 0c.034-.01.067-.02.1-.032l-.17-.47-.257.016-.243.015.001.014h-3.017a3.25 3.25 0 0 1-6.482 0H2a.75.75 0 0 1-.75-.75v-12A.75.75 0 0 1 2 3.75h12a.75.75 0 0 1 .75.75l-.423 13.207Zm7.423-4.547c0-.492-.096-.98-.285-1.436l-.424.176.424-.176a3.75 3.75 0 0 0-.811-1.217h0l-1.611-1.61a.5.5 0 0 0-.354-.147H14.75a.5.5 0 0 0-.5.5v6.017a.5.5 0 0 0 .923.267 2.75 2.75 0 0 1 4.867.408.5.5 0 0 0 .46.308h.75a.5.5 0 0 0 .5-.5v-2.59Zm0 0h-.5m.5 0v0h-.5m0 0v2.09l-.95-4.39a3.25 3.25 0 0 1 .95 2.3ZM14 16a.5.5 0 0 0-.433.75H10.74a.5.5 0 0 0-.499.462 2.75 2.75 0 0 1-5.484 0 .5.5 0 0 0-.499-.462H2a.25.25 0 0 1-.25-.25v-12A.25.25 0 0 1 2 4.25h12a.25.25 0 0 1 .25.25v3.25a.5.5 0 0 0 .5.5H19a.25.25 0 0 1 .177.073l1.83 1.83s0 0 0 0a4.253 4.253 0 0 1 1.243 3.007v3.34a.25.25 0 0 1-.25.25h-1.26a.5.5 0 0 0-.498.462 2.75 2.75 0 0 1-5.487-.038.5.5 0 0 0-.043-.174H15a.498.498 0 0 0 .251-.067L15.25 17a2.25 2.25 0 1 0 .148-.803A.5.5 0 0 0 15 16h-1Zm-9.5.25a.5.5 0 0 0 .46-.308 2.751 2.751 0 0 1 5.08 0 .5.5 0 0 0 .46.308h2.75a.5.5 0 0 0 .5-.5V5.25a.5.5 0 0 0-.5-.5H2.75a.5.5 0 0 0-.5.5v10.5a.5.5 0 0 0 .5.5H4.5Zm3-1.5a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5Z"
    />
  </svg>
)
