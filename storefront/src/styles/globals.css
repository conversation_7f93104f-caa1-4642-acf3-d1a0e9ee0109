@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@layer base {
  html {
    @apply text-black font-normal;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-medium;
  }
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

@layer components {
  .content-container {
    @apply max-w-[1440px] w-full mx-auto px-6;
  }

  .contrast-btn {
    @apply px-4 py-2 border border-black rounded-full hover:bg-black hover:text-white transition-colors duration-200 ease-in;
  }

  .text-xsmall-regular {
    @apply text-[10px] leading-4 font-normal;
  }

  .text-small-regular {
    @apply text-xs leading-5 font-normal;
  }

  .text-small-semi {
    @apply text-xs leading-5 font-semibold;
  }

  .text-base-regular {
    @apply text-sm leading-6 font-normal;
  }

  .text-base-semi {
    @apply text-sm leading-6 font-semibold;
  }

  .text-large-regular {
    @apply text-base leading-6 font-normal;
  }

  .text-large-semi {
    @apply text-base leading-6 font-semibold;
  }

  .text-xl-regular {
    @apply text-2xl leading-[36px] font-normal;
  }

  .text-xl-semi {
    @apply text-2xl leading-[36px] font-semibold;
  }

  .text-2xl-regular {
    @apply text-[30px] leading-[48px] font-normal;
  }

  .text-2xl-semi {
    @apply text-[30px] leading-[48px] font-semibold;
  }

  .text-3xl-regular {
    @apply text-[32px] leading-[44px] font-normal;
  }

  .text-3xl-semi {
    @apply text-[32px] leading-[44px] font-semibold;
  }

  .article {
    h1 {
      @apply text-2xl;
    }

    h2,
    h3,
    h4,
    h5,
    h6 {
      @apply text-md mt-16 mb-8;
    }

    p,
    ul {
      @apply mb-4;
    }

    ul,
    ol {
      @apply pl-5;
    }

    ul {
      @apply list-disc;
    }

    ol {
      @apply list-decimal;

      ul {
        @apply mt-4 pl-0;
      }

      > li {
        @apply pl-1;
      }

      > li + li {
        @apply mt-5;
      }
    }
  }

  .txt-medium {
    @apply text-[1.875rem] leading-[1.3125rem] font-normal font-inter;
  }

  .txt-xlarge-plus {
    @apply text-[1.125rem] leading-[1.6875rem] font-medium font-inter;
  }
}
